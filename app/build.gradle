plugins {
    alias(libs.plugins.androidApplication)
}

android {
    namespace 'com.example.soundtransmit'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.soundtransmit"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    externalNativeBuild {
        ndkBuild {
            path file('Android.mk')
        }
    }

    ndkVersion '25.2.9519653'
    packagingOptions{
        exclude 'META-INF/LICENSE.md'
        exclude 'META-INF/LICENSE-notice.md'
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}