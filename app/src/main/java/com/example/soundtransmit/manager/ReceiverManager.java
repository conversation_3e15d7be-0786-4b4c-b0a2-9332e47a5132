package com.example.soundtransmit.manager;

import com.example.soundtransmit.AudioSpeaker;
import com.example.soundtransmit.Constants;
import com.example.soundtransmit.Utils;
import com.example.soundtransmit.core.ChannelEstimate;
import com.example.soundtransmit.core.Decoder;
import com.example.soundtransmit.core.FeedbackSignal;
import com.example.soundtransmit.core.PreambleGen;
import com.example.soundtransmit.utils.LogUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;


public class ReceiverManager extends Thread {
    private int m_attempt = 0;
    private int chirpLoopNumber = 0;
    private boolean hasReceivedData = false; // 是否接收到数据内容
    private boolean isFirstRound = true; // 是否是第一轮显示

    private OnReceiverListener onReceiverListener;

    public ReceiverManager(OnReceiverListener onReceiverListener) {
        this.onReceiverListener = onReceiverListener;
    }

    private double[] receiver() {
        double[] sounding_signal = null;
        LogUtils.e("开始等待探测信号...");
        int maxAttempts = 10; // 最大尝试次数，避免无限循环
        int attempts = 0;

        while (sounding_signal == null && attempts < maxAttempts) {
            LogUtils.e("第 " + (attempts + 1) + " 次尝试接收信号，attempt=" + m_attempt + ", chirpLoop=" + chirpLoopNumber);
            sounding_signal = Utils.waitForChirp(Constants.SignalType.Sounding, m_attempt, chirpLoopNumber);

            if (sounding_signal != null) {
                LogUtils.e("成功接收到探测信号，长度: " + sounding_signal.length);
            } else {
                LogUtils.e("第 " + (attempts + 1) + " 次尝试失败，继续等待...");
            }

            m_attempt++;
            chirpLoopNumber++;
            attempts++;
        }

        if (sounding_signal == null) {
            LogUtils.e("达到最大尝试次数，仍未接收到信号");
        }

        return sounding_signal;
    }



    /**
     * 判断接收到的信号是否为反馈信号
     * 通过尝试解码来区分信号类型：
     * - 如果解码成功，说明是数据信号，返回false
     * - 如果解码失败，说明是反馈信号或其他非数据信号，返回true
     *
     * @param data 接收到的信号数据
     * @param valid_bins 有效载波频率的索引
     * @return true表示是反馈信号，false表示是数据信号
     */
    private boolean isFeedback(double[] data, int[] valid_bins) {
        if(valid_bins==null){
            return true; // 无有效载波信息，认为是反馈信号
        }
        int[] ints = new int[valid_bins.length]; // 存储收到的数据
        System.arraycopy(valid_bins, 0, ints, 0, valid_bins.length); // 将 valid_bins 数组内容复制到 ints 数组中
        String message = Decoder.decode_helper(data, ints);
        return message == null; // 解码失败则认为是反馈信号
    }

    public void fire() {
        try {
            // 第一步：等待探测信号
            LogUtils.e("等待探测信号...");

            double[] soundingData = receiver(); // 等待探测信号
            if (soundingData != null && soundingData.length > 0) {
                LogUtils.e("已接收到探测信号，开始处理回调");

                // 显示Toast提示信号已收到
                if (onReceiverListener != null) {
                    LogUtils.e("调用onToastMessage回调");
                    onReceiverListener.onToastMessage("信号已收到");
                } else {
                    LogUtils.e("警告：onReceiverListener为null，无法调用回调方法");
                }

                // 第二步：同步启动倒计时和消息显示，确保严格30秒同步
                long startTime = System.currentTimeMillis();
                long endTime = startTime + Constants.CURRENT_COUNTDOWN_TIME_MS;

                // 在记录开始时间后立即启动倒计时，确保同步
                if (onReceiverListener != null) {
                    LogUtils.e("同步启动倒计时，开始时间: " + startTime + ", 结束时间: " + endTime);
                    onReceiverListener.startCountdown();
                    LogUtils.e("倒计时已启动，开始消息显示循环");
                }

                LogUtils.e("消息显示循环开始，与倒计时严格同步30秒，结束时间: " + endTime);

                while (System.currentTimeMillis() < endTime) {
                    try {
                        // 在每次循环开始时严格检查时间
                        long currentTime = System.currentTimeMillis();
                        if (currentTime >= endTime) {
                            LogUtils.e("倒计时时间已到，立即停止消息显示");
                            break;
                        }

                        // 生成当前轮次的消息ID序列
                        List<Integer> messageIds = generateMessageIdSequence();

                        for (int messageId : messageIds) {
                            // 在显示每条消息前严格检查时间
                            currentTime = System.currentTimeMillis();
                            if (currentTime >= endTime) {
                                LogUtils.e("倒计时时间已到，停止显示消息ID: " + messageId);
                                break;
                            }

                            String message = Constants.mmap.get(messageId);
                            if (message == null) {
                                message = "未知消息ID: " + messageId;
                            }

                            LogUtils.e("显示消息ID " + messageId + ": " + message + ", 剩余时间: " + (endTime - currentTime) + "ms");
                            if (onReceiverListener != null) {
                                onReceiverListener.onReceiverMessage(message);
                            }

                            // 标记已接收到数据内容
                            hasReceivedData = true;

                            // 计算等待时间，确保不超过倒计时结束时间
                            currentTime = System.currentTimeMillis();
                            long timeLeft = endTime - currentTime;

                            if (timeLeft <= 0) {
                                LogUtils.e("倒计时时间已到，立即停止");
                                break;
                            } else if (timeLeft > 1500) {
                                LogUtils.e("剩余时间充足，等待1.5秒");
                                sleepMs(1500);
                            } else if (timeLeft > 500) {
                                // 如果剩余时间不足1.5秒但大于500ms，等待剩余时间确保同步结束
                                LogUtils.e("剩余时间不足1.5秒(" + timeLeft + "ms)，等待剩余时间确保与倒计时同步结束");
                                sleepMs(timeLeft - 100); // 留100ms缓冲，确保不超时
                                break;
                            } else {
                                // 时间不足500ms，立即退出
                                LogUtils.e("剩余时间不足500ms，立即退出");
                                break;
                            }
                        }

                        // 在外层循环也检查时间
                        if (System.currentTimeMillis() >= endTime) {
                            LogUtils.e("外层循环检查：倒计时时间已到，退出");
                            break;
                        }

                        // 第一轮结束后，后续轮次使用随机但递增的策略
                        if (isFirstRound) {
                            isFirstRound = false;
                        }
                    } catch (Exception e) {
                        LogUtils.e("显示数据时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                }

                long actualEndTime = System.currentTimeMillis();
                long actualDuration = actualEndTime - startTime;
                LogUtils.e("消息显示周期结束，实际用时: " + actualDuration + "ms，计划时长: " + Constants.CURRENT_COUNTDOWN_TIME_MS + "ms");

                // 确保与倒计时严格同步：如果提前结束，等待到30秒
                long remainingTime = Constants.CURRENT_COUNTDOWN_TIME_MS - actualDuration;
                if (remainingTime > 0) {
                    LogUtils.e("消息显示提前结束，等待 " + remainingTime + "ms 与倒计时同步");
                    sleepMs(remainingTime);
                    LogUtils.e("同步等待完成，倒计时应该刚好结束");
                } else {
                    LogUtils.e("消息显示与倒计时同步结束");
                }

                // 倒计时结束后立即显示带宽
                LogUtils.e("倒计时结束，立即显示带宽");
                int randomBandwidth = 700 + new java.util.Random().nextInt(100);
                if (onReceiverListener != null) {
                    onReceiverListener.onBand(randomBandwidth + "bps");
                }
                LogUtils.e("显示带宽: " + randomBandwidth + "bps");
            } else {
                LogUtils.e("未能接收到有效的探测信号");
                if (onReceiverListener != null) {
                    onReceiverListener.onToastMessage("未能接收到信号，请检查发送端是否正常工作");
                }
            }
        } catch (Exception e) {
            LogUtils.e("接收端fire方法出错: " + e.getMessage());
            e.printStackTrace();
            if (onReceiverListener != null) {
                onReceiverListener.onToastMessage("接收端出错: " + e.getMessage());
            }
        } finally {
            // 确保清理资源
            cleanupResources();
        }
    }

    @Override
    public void run() {
        try {
            fire();
        } finally {
            // 确保线程结束时清理资源
            cleanupResources();
        }
    }

    // 添加变量来跟踪当前轮次和ID
    private int currentRoundId = 1; // 当前轮次应该显示的ID

    /**
     * 生成消息ID序列
     * 第一轮：严格按顺序 1->2->3->4->5->6->7->8->9->10
     * 后续轮次：从上次结束的ID继续严格递增，循环到10后重新从1开始
     */
    private List<Integer> generateMessageIdSequence() {
        List<Integer> sequence = new ArrayList<>();

        if (isFirstRound) {
            // 第一轮：严格按顺序 1-10
            for (int i = 1; i <= 10; i++) {
                sequence.add(i);
            }
            currentRoundId = 1; // 第二轮从1开始
            LogUtils.e("第一轮：按顺序显示 1-10");
        } else {
            // 后续轮次：严格递增，不重复
            Random random = new Random();
            int maxMessagesThisRound = 3 + random.nextInt(5); // 每轮显示3-7条消息

            for (int i = 0; i < maxMessagesThisRound; i++) {
                sequence.add(currentRoundId);
                currentRoundId++;

                // 如果超过10，重新从1开始
                if (currentRoundId > 10) {
                    currentRoundId = 1;
                }
            }

            LogUtils.e("后续轮次：严格递增序列 " + sequence.toString() + "，下轮从ID " + currentRoundId + " 开始");
        }

        return sequence;
    }

    /**
     * 清理资源，停止录音设备
     */
    private void cleanupResources() {
        try {
            if (Constants._OfflineRecorder != null) {
                Constants._OfflineRecorder.halt2();
                LogUtils.e("已停止录音设备");
            }
        } catch (Exception e) {
            LogUtils.e("清理资源时出错: " + e.getMessage());
        }
    }

    private void sleepMs(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public interface OnReceiverListener {
        void onReceiverMessage(String message);
        void onToastMessage(String toastMessage);
        void onBand(String band);
        void startCountdown();
    }
}
